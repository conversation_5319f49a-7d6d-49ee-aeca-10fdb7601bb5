csdn:
  api:
    categories: Java场景面试宝典
    cookie: uuid_tt_dd=10_10320122900-*************-677449; dc_session_id=10_*************.409613; c_pref=default; fid=20_45104174745-*************-582667; c_first_ref=default; c_first_page=https%3A//blog.csdn.net/AppleWebCoder/article/details/*********%3Fspm%3D1001.2014.3001.5502; c_dsid=11_*************.481835; c_segment=9; creative_btn_mp=1; loginbox_strategy=%7B%22blog-threeH-dialog-exp11tipShowTimes%22%3A1%7D; popPageViewTimes=1; Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac=**********; HMACCOUNT=2BA38DD489738EC6; hide_login=1; dc_sid=e2679e3c8f8cfece0b83262d6cad9974; _clck=1is2jgo%5E2%5Efyw%5E0%5E2068; SESSION=9cfa82d4-0af7-4241-a81e-5b00aa0c7082; UserName=AppleWebCoder; UserInfo=85d515c013284d6a852b7d35b932e04e; UserToken=85d515c013284d6a852b7d35b932e04e; UserNick=AppleWebCoder; AU=AE2; UN=AppleWebCoder; BT=*************; p_uid=U010000; csdn_newcert_AppleWebCoder=1; tfstk=gXfrLvDHv7FP-MibNidEbc_t4TOJsBr_L6tB-wbHP3xkO8KHxwIvRX_32HYF0n9CFHOkxWSGcMJLyHtHYM_hFrN_1aQJ9BDUf5N1u9elLgooZB4HmF8glUjNNyop9Bq_ccl3O3OdqF4-XBb0ueTItYjHEK42WFAnZpconmxv-BAHEHYmiUTixbckxr7DDeAHxMA3uIxv-BxhxBcJLgIKYFvuoWmKefzfVL-GrnlNks8lkYQy00mlgNJyjW-qtXf2zwy6UqcadhX9VdtNmSGyTZYF0d6zbbSF-NBH36ouPGbl21BhzSrMN_QlIpfgvvjwaMJGZKun6QRDmI8csyM9cs-5x_J05fJBi19MZtwYVpOy7MfR42lhjZ_1wKCUbmxdFFdD86UiLHJh4gipoBLN9TonLLYvuha4un8Oo930DbnrpvpDIE-_ydHKpKfeuha4RvHpnU82fzpN.; c_page_id=default; log_Id_pv=4; creativeSetApiNew=%7B%22toolbarImg%22%3A%22https%3A//img-home.csdnimg.cn/images/**************.png%22%2C%22publishSuccessImg%22%3A%22https%3A//img-home.csdnimg.cn/images/20240229024608.png%22%2C%22articleNum%22%3A0%2C%22type%22%3A0%2C%22oldUser%22%3Afalse%2C%22useSeven%22%3Atrue%2C%22oldFullVersion%22%3Afalse%2C%22userName%22%3A%22AppleWebCoder%22%7D; _clsk=zwogjs%5E1756520927041%5E2%5E0%5Ek.clarity.ms%2Fcollect; log_Id_click=5; dc_tos=t1sc87; c_ref=https%3A//blog.csdn.net/AppleWebCoder/article/details/*********%3Fspm%3D1001.2014.3001.5502; Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac=1756520936; log_Id_view=90

spring:
  application:
    name: mcp-server-csdn

  ai:
    mcp:
      server:
        name: ${spring.application.name}
        version: 1.0.0

  main:
    banner-mode: off
# stdio 模式打开，sse 模式，注释掉。
    web-application-type: none

logging:
# stdio 模式打开，sse 模式，注释掉。
#  pattern:
#    console:
  file:
    name: data/log/${spring.application.name}.log

server:
  port: 8101
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
